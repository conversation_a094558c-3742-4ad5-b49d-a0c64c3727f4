---
import type { CollectionEntry } from 'astro:content'
import Pagination from './Pagination.astro'

export interface Props {
  photos: CollectionEntry<'photos'>[]
  currentPage: number
  totalPages: number
}

const { photos, currentPage, totalPages } = Astro.props

// 按日期排序（最新的在前）
const sortedPhotos = photos.sort((a: CollectionEntry<'photos'>, b: CollectionEntry<'photos'>) =>
  new Date(b.data.date).getTime() - new Date(a.data.date).getTime()
)
---

<section class="photo-gallery">


  {sortedPhotos.length > 0 ? (
    <div class="photos-grid-display">
      <!-- 照片单列展示 -->
      <div class="photos-list space-y-8 mb-8">
        {sortedPhotos.map((photo: CollectionEntry<'photos'>) => (
          <div class="photo-item">
            <img
              src={photo.data.url}
              alt={photo.data.alt}
              class="w-full max-w-4xl mx-auto object-contain"
              loading="lazy"
            />
          </div>
        ))}
      </div>

      <!-- 分页导航 -->
      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPage={totalPages}
          baseUrl="/gallery/page/"
          leftUrl={currentPage === 2 ? '/gallery' : `/gallery/page/${currentPage - 1}`}
          rightUrl={`/gallery/page/${currentPage + 1}`}
          showPageCount={false}
        />
      )}
    </div>
  ) : (
    <div class="empty-state text-center py-16">
      <div class="text-6xl mb-4 opacity-50">📷</div>
      <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
        No photos yet
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mb-4">
        Start adding some photos to your gallery.
      </p>
    </div>
  )}
</section>

<style>
  .photos-grid {
    @apply auto-rows-max;
  }

  .photo-item img {
    @apply transition-opacity duration-300 hover:opacity-80;
  }

  .pagination a {
    @apply no-underline;
  }
</style>
